2025-08-02 09:57:52.066 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Injector.resolveConstructorParams (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-02 09:57:52.125 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.125 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.125 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.125 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.126 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.126 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.126 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.126 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.126 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.127 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.127 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.127 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.128 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.128 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.128 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.129 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.129 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.129 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.130 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.130 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:00:39.106 [ERROR] [GlobalExceptionFilter] GET /api/user/class/teacher/2896/stats - Cannot GET /api/user/class/teacher/2896/stats {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai"}
NotFoundException: Cannot GET /api/user/class/teacher/2896/stats
    at callback (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:342:13)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:297:9
    at processParams (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:582:12)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (C:\Users\<USER>\Desktop\LL\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-02 10:00:39.107 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai"}
{"url":"/api/user/class/teacher/2896/stats","method":"GET","statusCode":404,"message":"Cannot GET /api/user/class/teacher/2896/stats","details":{"message":"Cannot GET /api/user/class/teacher/2896/stats","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T02:00:39.107Z"}
2025-08-02 10:09:55.022 [ERROR] [GlobalExceptionFilter] GET /api/user/class/teacher/classes/28?teacherId=2896 - read ECONNRESET {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai"}
HttpException: read ECONNRESET
    at UserClassController.getTeacherClasses (C:\Users\<USER>\Desktop\LL\logic-back\src\web\user_class\user_class.controller.ts:860:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
2025-08-02 10:09:55.022 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai"}
{"url":"/api/user/class/teacher/classes/28?teacherId=2896","method":"GET","statusCode":400,"message":"read ECONNRESET","details":{},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T02:09:55.022Z"}
2025-08-02 10:14:01.958 [ERROR] [GlobalExceptionFilter] GET /api/user/srch/templates/current/2896 - read ECONNRESET {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai"}
QueryFailedError: read ECONNRESET
    at Query.onResult (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\typeorm\driver\src\driver\mysql\MysqlQueryRunner.ts:243:33)
    at PoolConnection._notifyError (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\mysql2\lib\base\connection.js:223:21)
    at PoolConnection._handleFatalError (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\mysql2\lib\base\connection.js:178:10)
    at PoolConnection._handleNetworkError (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\mysql2\lib\base\connection.js:191:10)
    at Socket.emit (node:events:518:28)
    at emitErrorNT (node:internal/streams/destroy:170:8)
    at emitErrorCloseNT (node:internal/streams/destroy:129:3)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)
2025-08-02 10:14:01.958 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai"}
{"url":"/api/user/srch/templates/current/2896","method":"GET","statusCode":500,"message":"read ECONNRESET","details":{"name":"QueryFailedError","stack":"QueryFailedError: read ECONNRESET\n    at Query.onResult (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\typeorm\\driver\\src\\driver\\mysql\\MysqlQueryRunner.ts:243:33)\n    at PoolConnection._notifyError (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\mysql2\\lib\\base\\connection.js:223:21)\n    at PoolConnection._handleFatalError (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\mysql2\\lib\\base\\connection.js:178:10)\n    at PoolConnection._handleNetworkError (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\mysql2\\lib\\base\\connection.js:191:10)\n    at Socket.emit (node:events:518:28)\n    at emitErrorNT (node:internal/streams/destroy:170:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:129:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T02:14:01.958Z"}
2025-08-02 10:14:02.000 [ERROR] [GlobalExceptionFilter] GET /api/web/user/info/2896 - read ECONNRESET {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai"}
QueryFailedError: read ECONNRESET
    at Query.onResult (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\typeorm\driver\src\driver\mysql\MysqlQueryRunner.ts:243:33)
    at PoolConnection._notifyError (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\mysql2\lib\base\connection.js:223:21)
    at PoolConnection._handleFatalError (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\mysql2\lib\base\connection.js:178:10)
    at PoolConnection._handleNetworkError (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\mysql2\lib\base\connection.js:191:10)
    at Socket.emit (node:events:518:28)
    at emitErrorNT (node:internal/streams/destroy:170:8)
    at emitErrorCloseNT (node:internal/streams/destroy:129:3)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)
2025-08-02 10:14:02.000 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai"}
{"url":"/api/web/user/info/2896","method":"GET","statusCode":500,"message":"read ECONNRESET","details":{"name":"QueryFailedError","stack":"QueryFailedError: read ECONNRESET\n    at Query.onResult (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\typeorm\\driver\\src\\driver\\mysql\\MysqlQueryRunner.ts:243:33)\n    at PoolConnection._notifyError (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\mysql2\\lib\\base\\connection.js:223:21)\n    at PoolConnection._handleFatalError (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\mysql2\\lib\\base\\connection.js:178:10)\n    at PoolConnection._handleNetworkError (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\mysql2\\lib\\base\\connection.js:191:10)\n    at Socket.emit (node:events:518:28)\n    at emitErrorNT (node:internal/streams/destroy:170:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:129:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T02:14:02.000Z"}
2025-08-02 10:14:02.012 [ERROR] [GlobalExceptionFilter] GET /api/user/class/teacher/2896/stats - Cannot GET /api/user/class/teacher/2896/stats {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai"}
NotFoundException: Cannot GET /api/user/class/teacher/2896/stats
    at callback (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:342:13)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:297:9
    at processParams (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:582:12)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (C:\Users\<USER>\Desktop\LL\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-02 10:14:02.012 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai"}
{"url":"/api/user/class/teacher/2896/stats","method":"GET","statusCode":404,"message":"Cannot GET /api/user/class/teacher/2896/stats","details":{"message":"Cannot GET /api/user/class/teacher/2896/stats","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T02:14:02.012Z"}
2025-08-02 10:20:29.868 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Injector.resolveConstructorParams (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-02 10:20:29.912 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:20:29.912 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:20:29.912 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:20:29.912 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:20:29.912 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:20:29.913 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:20:29.913 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:20:29.913 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:20:29.913 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:20:29.913 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:20:29.913 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:20:29.914 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:20:29.914 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:20:29.914 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:20:29.914 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:20:29.914 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:20:29.915 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:20:29.915 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:20:29.915 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:20:29.915 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:22:23.777 [ERROR] [GlobalExceptionFilter] GET /api/user/class/teacher/2896/stats - Cannot GET /api/user/class/teacher/2896/stats {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai"}
NotFoundException: Cannot GET /api/user/class/teacher/2896/stats
    at callback (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:342:13)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:297:9
    at processParams (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:582:12)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (C:\Users\<USER>\Desktop\LL\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-02 10:22:23.778 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai"}
{"url":"/api/user/class/teacher/2896/stats","method":"GET","statusCode":404,"message":"Cannot GET /api/user/class/teacher/2896/stats","details":{"message":"Cannot GET /api/user/class/teacher/2896/stats","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T02:22:23.778Z"}
2025-08-02 10:23:02.736 [ERROR] [GlobalExceptionFilter] GET /api/user/class/teacher/2896/stats - Cannot GET /api/user/class/teacher/2896/stats {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai"}
NotFoundException: Cannot GET /api/user/class/teacher/2896/stats
    at callback (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:342:13)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:297:9
    at processParams (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:582:12)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (C:\Users\<USER>\Desktop\LL\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-02 10:23:02.736 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai"}
{"url":"/api/user/class/teacher/2896/stats","method":"GET","statusCode":404,"message":"Cannot GET /api/user/class/teacher/2896/stats","details":{"message":"Cannot GET /api/user/class/teacher/2896/stats","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T02:23:02.736Z"}
2025-08-02 10:32:11.502 [ERROR] [GlobalExceptionFilter] GET /api/teacher-task/list?teacherId=2896&page=1&size=50&orderBy=createTime:DESC - read ECONNRESET {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai"}
QueryFailedError: read ECONNRESET
    at Query.onResult (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\typeorm\driver\src\driver\mysql\MysqlQueryRunner.ts:243:33)
    at PoolConnection._notifyError (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\mysql2\lib\base\connection.js:223:21)
    at PoolConnection._handleFatalError (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\mysql2\lib\base\connection.js:178:10)
    at PoolConnection._handleNetworkError (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\mysql2\lib\base\connection.js:191:10)
    at Socket.emit (node:events:518:28)
    at emitErrorNT (node:internal/streams/destroy:170:8)
    at emitErrorCloseNT (node:internal/streams/destroy:129:3)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)
2025-08-02 10:32:11.502 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai"}
{"url":"/api/teacher-task/list?teacherId=2896&page=1&size=50&orderBy=createTime:DESC","method":"GET","statusCode":500,"message":"read ECONNRESET","details":{"name":"QueryFailedError","stack":"QueryFailedError: read ECONNRESET\n    at Query.onResult (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\typeorm\\driver\\src\\driver\\mysql\\MysqlQueryRunner.ts:243:33)\n    at PoolConnection._notifyError (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\mysql2\\lib\\base\\connection.js:223:21)\n    at PoolConnection._handleFatalError (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\mysql2\\lib\\base\\connection.js:178:10)\n    at PoolConnection._handleNetworkError (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\mysql2\\lib\\base\\connection.js:191:10)\n    at Socket.emit (node:events:518:28)\n    at emitErrorNT (node:internal/streams/destroy:170:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:129:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T02:32:11.502Z"}
2025-08-02 10:44:06.360 [ERROR] [GlobalExceptionFilter] GET /api/user/class/teacher/2896/stats - Cannot GET /api/user/class/teacher/2896/stats {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai"}
NotFoundException: Cannot GET /api/user/class/teacher/2896/stats
    at callback (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:342:13)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:297:9
    at processParams (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:582:12)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (C:\Users\<USER>\Desktop\LL\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-02 10:44:06.360 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35280,"hostname":"zhulezai"}
{"url":"/api/user/class/teacher/2896/stats","method":"GET","statusCode":404,"message":"Cannot GET /api/user/class/teacher/2896/stats","details":{"message":"Cannot GET /api/user/class/teacher/2896/stats","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T02:44:06.360Z"}
2025-08-02 10:46:29.811 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Injector.resolveConstructorParams (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-02 10:46:29.861 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:46:29.862 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:46:29.862 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:46:29.862 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:46:29.862 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:46:29.863 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:46:29.863 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:46:29.863 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:46:29.863 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:46:29.864 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:46:29.864 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:46:29.864 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:46:29.864 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:46:29.865 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:46:29.865 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:46:29.865 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:46:29.865 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:46:29.865 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:46:29.866 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:46:29.866 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:51:22.330 [ERROR] [GlobalExceptionFilter] GET /api/encryption/publicKey - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-08-02 10:51:22.332 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
{"url":"/api/encryption/publicKey","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T02:51:22.331Z"}
2025-08-02 10:51:22.353 [ERROR] [GlobalExceptionFilter] GET /api/teacher-task/list?teacherId=2896&page=1&size=50&orderBy=createTime:DESC - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-08-02 10:51:22.354 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
{"url":"/api/teacher-task/list?teacherId=2896&page=1&size=50&orderBy=createTime:DESC","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T02:51:22.354Z"}
2025-08-02 10:51:22.360 [ERROR] [GlobalExceptionFilter] GET /api/user/srch/templates/current/2896 - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-08-02 10:51:22.361 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
{"url":"/api/user/srch/templates/current/2896","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T02:51:22.360Z"}
2025-08-02 10:51:22.365 [ERROR] [GlobalExceptionFilter] GET /api/user-school/listByUserId - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-08-02 10:51:22.366 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
{"url":"/api/user-school/listByUserId","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T02:51:22.366Z"}
2025-08-02 10:51:22.374 [ERROR] [GlobalExceptionFilter] GET /api/teacher-task/list?teacherId=2896&page=1&size=50&orderBy=createTime:DESC - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-08-02 10:51:22.375 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
{"url":"/api/teacher-task/list?teacherId=2896&page=1&size=50&orderBy=createTime:DESC","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T02:51:22.375Z"}
2025-08-02 10:51:22.379 [ERROR] [GlobalExceptionFilter] GET /api/user/srch/templates/current/2896 - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-08-02 10:51:22.380 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
{"url":"/api/user/srch/templates/current/2896","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T02:51:22.379Z"}
2025-08-02 10:51:22.384 [ERROR] [GlobalExceptionFilter] GET /api/user-school/listByUserId - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-08-02 10:51:22.385 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
{"url":"/api/user-school/listByUserId","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T02:51:22.385Z"}
2025-08-02 10:51:22.406 [ERROR] [GlobalExceptionFilter] GET /api/encryption/publicKey - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-08-02 10:51:22.407 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
{"url":"/api/encryption/publicKey","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T02:51:22.407Z"}
2025-08-02 10:51:22.439 [ERROR] [GlobalExceptionFilter] POST /api/router-guard/refresh-token - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.refreshToken (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:203:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async RouterGuardController.refreshToken (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.controller.ts:63:22)
2025-08-02 10:51:22.440 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
{"url":"/api/router-guard/refresh-token","method":"POST","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"用户信息不存在","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T02:51:22.439Z"}
2025-08-02 10:51:22.483 [ERROR] [GlobalExceptionFilter] GET /api/user/class/teacher/2896/stats - Cannot GET /api/user/class/teacher/2896/stats {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
NotFoundException: Cannot GET /api/user/class/teacher/2896/stats
    at callback (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:342:13)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:297:9
    at processParams (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:582:12)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (C:\Users\<USER>\Desktop\LL\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-02 10:51:22.484 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
{"url":"/api/user/class/teacher/2896/stats","method":"GET","statusCode":404,"message":"Cannot GET /api/user/class/teacher/2896/stats","details":{"message":"Cannot GET /api/user/class/teacher/2896/stats","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T02:51:22.483Z"}
2025-08-02 10:51:22.561 [ERROR] [GlobalExceptionFilter] GET /api/user-point/total - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-08-02 10:51:22.562 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
{"url":"/api/user-point/total","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T02:51:22.561Z"}
2025-08-02 10:55:38.215 [ERROR] [GlobalExceptionFilter] GET /api/user-school/listByUserId - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-08-02 10:55:38.215 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
{"url":"/api/user-school/listByUserId","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T02:55:38.215Z"}
2025-08-02 10:55:38.225 [ERROR] [GlobalExceptionFilter] GET /api/user-school/listByUserId - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-08-02 10:55:38.225 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
{"url":"/api/user-school/listByUserId","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T02:55:38.225Z"}
2025-08-02 10:58:33.931 [ERROR] [GlobalExceptionFilter] GET /api/user-school/listByUserId - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-08-02 10:58:33.932 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
{"url":"/api/user-school/listByUserId","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T02:58:33.931Z"}
2025-08-02 10:58:33.946 [ERROR] [GlobalExceptionFilter] GET /api/user-school/listByUserId - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-08-02 10:58:33.946 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
{"url":"/api/user-school/listByUserId","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T02:58:33.946Z"}
2025-08-02 11:08:21.817 [ERROR] [GlobalExceptionFilter] GET /api/user-school/listByUserId - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-08-02 11:08:21.818 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
{"url":"/api/user-school/listByUserId","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:08:21.817Z"}
2025-08-02 11:08:21.820 [ERROR] [GlobalExceptionFilter] GET /api/user-school/listByUserId - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (C:\Users\<USER>\Desktop\LL\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:435:11)
2025-08-02 11:08:21.821 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
{"url":"/api/user-school/listByUserId","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:08:21.821Z"}
2025-08-02 11:08:47.867 [ERROR] [GlobalExceptionFilter] GET /api/user/class/teacher/2896/stats - Cannot GET /api/user/class/teacher/2896/stats {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
NotFoundException: Cannot GET /api/user/class/teacher/2896/stats
    at callback (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:342:13)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:297:9
    at processParams (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:582:12)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (C:\Users\<USER>\Desktop\LL\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-02 11:08:47.867 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
{"url":"/api/user/class/teacher/2896/stats","method":"GET","statusCode":404,"message":"Cannot GET /api/user/class/teacher/2896/stats","details":{"message":"Cannot GET /api/user/class/teacher/2896/stats","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:08:47.867Z"}
2025-08-02 11:08:57.272 [ERROR] [GlobalExceptionFilter] GET /api/user/class/teacher/2896/stats - Cannot GET /api/user/class/teacher/2896/stats {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
NotFoundException: Cannot GET /api/user/class/teacher/2896/stats
    at callback (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:342:13)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:297:9
    at processParams (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:582:12)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (C:\Users\<USER>\Desktop\LL\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-02 11:08:57.272 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
{"url":"/api/user/class/teacher/2896/stats","method":"GET","statusCode":404,"message":"Cannot GET /api/user/class/teacher/2896/stats","details":{"message":"Cannot GET /api/user/class/teacher/2896/stats","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:08:57.272Z"}
2025-08-02 11:09:47.877 [ERROR] [GlobalExceptionFilter] GET /api/user/class/teacher/2896/stats - Cannot GET /api/user/class/teacher/2896/stats {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
NotFoundException: Cannot GET /api/user/class/teacher/2896/stats
    at callback (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:342:13)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:297:9
    at processParams (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:582:12)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (C:\Users\<USER>\Desktop\LL\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-02 11:09:47.877 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
{"url":"/api/user/class/teacher/2896/stats","method":"GET","statusCode":404,"message":"Cannot GET /api/user/class/teacher/2896/stats","details":{"message":"Cannot GET /api/user/class/teacher/2896/stats","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:09:47.877Z"}
2025-08-02 11:09:56.199 [ERROR] [GlobalExceptionFilter] GET /api/user/class/teacher/2896/stats - Cannot GET /api/user/class/teacher/2896/stats {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
NotFoundException: Cannot GET /api/user/class/teacher/2896/stats
    at callback (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:342:13)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:297:9
    at processParams (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:582:12)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (C:\Users\<USER>\Desktop\LL\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-02 11:09:56.199 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
{"url":"/api/user/class/teacher/2896/stats","method":"GET","statusCode":404,"message":"Cannot GET /api/user/class/teacher/2896/stats","details":{"message":"Cannot GET /api/user/class/teacher/2896/stats","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:09:56.199Z"}
2025-08-02 11:14:54.612 [ERROR] [GlobalExceptionFilter] GET /api/user/class/teacher/2896/stats - Cannot GET /api/user/class/teacher/2896/stats {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
NotFoundException: Cannot GET /api/user/class/teacher/2896/stats
    at callback (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:342:13)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:297:9
    at processParams (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:582:12)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (C:\Users\<USER>\Desktop\LL\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-02 11:14:54.613 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":41052,"hostname":"zhulezai"}
{"url":"/api/user/class/teacher/2896/stats","method":"GET","statusCode":404,"message":"Cannot GET /api/user/class/teacher/2896/stats","details":{"message":"Cannot GET /api/user/class/teacher/2896/stats","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:14:54.612Z"}
